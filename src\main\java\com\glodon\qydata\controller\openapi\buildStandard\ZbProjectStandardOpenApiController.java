package com.glodon.qydata.controller.openapi.buildStandard;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.service.standard.buildStandard.IZbProjectStandardService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 　　* @description: 项目标准控制层 -对外接口
 　　* <AUTHOR>
 　　* @date 2021/8/16 16:55
 　　*/
@RestController
@RequestMapping("/basicInfo/openApi/standards/buildStandard")
@Tag(name = "项目标准控制层 -对外接口", description = "项目标准控制层 -对外接口")
public class ZbProjectStandardOpenApiController extends BaseController {

    @Autowired
    private IZbProjectStandardService zbProjectStandardService;

    /**
     * 获取企业下的建造标准列表(对外接口)
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @return {@link ResponseVo< List<  ZbProjectStandard >>}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 9:32
     */
    @Operation(summary = "获取企业下的建造标准列表")
    @GetMapping("/standardList/foreign")
    public ResponseVo<List<ZbProjectStandard>> getStandardListForeign(@RequestParam @Nullable Integer isShowDelete, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.standardList(customerCode, isShowDelete, Constants.ORDER_ASC, true));
    }~

    /**
     * 根据建造标准id获取建造标准树形表格(对外接口)
     * @throws
     * @param standardId
     * @param dataType 返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * <AUTHOR>
     * @return {@link ResponseVo< List<  ZbProjectStandardDetailTreeDto >>}
     * @date 2021/8/18 17:06
     */
    @Operation(summary = "根据建造标准id获取建造标准树形表格")
    @GetMapping("/data/foreign/{buildStandardId}")
    public ResponseVo<List<ZbProjectStandardDetailTreeDto>> getDetailTreeForeign(@PathVariable("buildStandardId") Long standardId,
                                                                                 @RequestParam @Nullable Integer dataType) {
        return ResponseVo.success(zbProjectStandardService.getDetailTree(standardId, dataType));
    }

    /**
     * 根据业态编码获取其下的建造标准
     * @throws
     * @param code
     * <AUTHOR>
     * @return {@link ResponseVo< List< ZbProjectStandard>>}
     * @date 2022/1/18 10:15
     */
    @Operation(summary = "根据业态编码获取其下的建造标准")
    @GetMapping("/categoryCode")
    public ResponseVo<List<ZbProjectStandard>> getByCategoryCode(@RequestParam @NotNull String code, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.getByCategoryCode(customerCode, code, true));
    }

    /**
     * 根据建造标准id获取建造标准明细集合
     * @param standardIdList
     * @return
     */
    @Operation(summary = "根据建造标准id获取建造标准明细集合")
    @GetMapping("/detail/byStandardIdList")
    public ResponseVo getDetailByStandardIdList(@RequestParam @NotNull List<Long> standardIdList) {
        return  ResponseVo.success(zbProjectStandardService.getDetailByStandardIdList(standardIdList));
    }

    /**
     * 根据企业和指定业态集合，查找所有集合
     * @param categoryCodeList
     * @return zb_standards_build_standard表集合
     */
    @Operation(summary = "根据企业和指定业态集合，查找所有集合")
    @GetMapping("/standardList/byCategoryCodes")
    public ResponseVo<List<ZbProjectStandard>> selectListByCategoryCodes(@RequestParam @NotNull List<String> categoryCodeList, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return  ResponseVo.success(zbProjectStandardService.getStandardByCategoryCodes(customerCode, categoryCodeList));
    }

    /**
     * 根据业态编码模糊查询建造标准
     * @param categoryCode 业态编码
     * @return 建造标准d集合
     */
    @Operation(summary = "根据业态编码模糊查询建造标准")
    @GetMapping("/standardList/likeCategoryCode")
    public ResponseVo selectListLikeCategoryCode(@RequestParam @NotNull String categoryCode, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.getStandardLikeCategoryCode(customerCode, categoryCode));
    }
}
